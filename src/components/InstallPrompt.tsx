'use client';

import { useEffect, useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { isPwaInstallable, setupInstallHandler, debugInstallPwa } from '@/utils/pwaUtils';

const InstallPrompt = () => {
  const [, setIsInstallable] = useState(false);
  const [isInstalling, setIsInstalling] = useState(false);
  const [installError, setInstallError] = useState<string | null>(null);

  const router = useRouter();
  const searchParams = useSearchParams();
  const isDebugMode = searchParams.get('debug') === 'true';

  useEffect(() => {
    // Check for debug bypass in localStorage
    const debugBypass = localStorage.getItem('loggedin');
    if (debugBypass === 'true') {
      router.push('/pairing/qr-scan');
      return;
    }

    // Set up the install handler
    setupInstall<PERSON>and<PERSON>();

    // Check if the app is installable
    const checkInstallable = () => {
      const canInstall = isPwaInstallable();
      console.log('App installable:', canInstall);
      setIsInstallable(canInstall);
    };

    // Check immediately
    checkInstallable();

    // Listen for changes in installability
    const handleInstallable = () => {
      console.log('pwaInstallable event received');
      setIsInstallable(true);
    };

    window.addEventListener('pwaInstallable', handleInstallable);

    // Check again after a short delay (some browsers need time)
    const timer = setTimeout(checkInstallable, 1000);

    return () => {
      window.removeEventListener('pwaInstallable', handleInstallable);
      clearTimeout(timer);
    };
  }, [router]);

  const handleInstallClick = async () => {
    setInstallError(null);
    setIsInstalling(true);

    try {
      console.log('Attempting to install...');
      debugInstallPwa();

      setTimeout(() => {
        setIsInstalling(false);
      }, 3000);
    } catch (error) {
      console.error('Installation failed:', error);
      setInstallError('Installation failed. Please try again or use the browser menu.');
      setIsInstalling(false);
    }
  };

  const handleDebugBypass = () => {
    localStorage.setItem('loggedin', 'true');
    router.push('/pairing/qr-scan');
  };

  return (
    <div className="flex flex-col items-center justify-center min-h-screen p-6 bg-gray-50 text-center">
      <div className="max-w-md w-full bg-white rounded-xl shadow-lg p-8">
        <div className="mb-8">
          {/* Manufacturing/Factory Icon */}
          <div className="w-24 h-24 mx-auto mb-6 bg-blue-600 rounded-xl flex items-center justify-center">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-12 h-12 text-white">
              <path strokeLinecap="round" strokeLinejoin="round" d="M8.25 21v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21m0 0h4.5V9.75a.75.75 0 0 1 .75-.75h2.25a.75.75 0 0 1 .75.75v11.25m-18 0A2.25 2.25 0 0 0 2.25 21h19.5a2.25 2.25 0 0 0 2.25-2.25m-19.5 0v-7.5A2.25 2.25 0 0 1 4.5 9h15a2.25 2.25 0 0 1 2.25 2.25v7.5" />
            </svg>
          </div>

          <h1 className="text-3xl font-bold mb-2 text-gray-900">Fabrication</h1>
        </div>

        <div className="space-y-6">
          <div className="bg-gray-50 p-6 rounded-xl">
            <h2 className="font-semibold text-xl mb-3 text-gray-900">Install the App</h2>
            <p className="text-gray-700 mb-5">
              Install this app on your device for the best experience.
            </p>

            {installError && (
              <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg text-red-700 text-sm">
                {installError}
              </div>
            )}

            <button
              onClick={handleInstallClick}
              disabled={isInstalling}
              className="w-full bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 transition-colors font-medium text-lg disabled:opacity-70 mb-4"
            >
              {isInstalling ? (
                <span className="flex items-center justify-center">
                  <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Installing...
                </span>
              ) : 'Install App'}
            </button>

            {/* Manual installation instructions */}
            <div className="bg-white p-4 rounded-lg border border-gray-200">
              <p className="font-medium text-gray-900 mb-3">Manual installation:</p>
              <ul className="space-y-2 text-sm">
                <li className="flex items-start">
                  <span className="inline-block w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                  <span className="text-gray-900"><strong className="text-gray-900">iOS:</strong> Tap share icon → "Add to Home Screen"</span>
                </li>
                <li className="flex items-start">
                  <span className="inline-block w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                  <span className="text-gray-900"><strong className="text-gray-900">Android:</strong> Menu → "Install App"</span>
                </li>
                <li className="flex items-start">
                  <span className="inline-block w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                  <span className="text-gray-900"><strong className="text-gray-900">Desktop:</strong> Install icon in address bar</span>
                </li>
              </ul>
            </div>
          </div>

        </div>
      </div>

      <div className="mt-8 flex flex-col items-center space-y-4">
        <p className="text-gray-600 text-sm">
          Fabrication • Manufacturing Management
        </p>

        {isDebugMode && (
          <button
            onClick={handleDebugBypass}
            className="text-sm bg-orange-200 hover:bg-orange-300 text-orange-900 py-2 px-4 rounded-lg border border-orange-400 font-medium"
          >
            Debug: Skip Install
          </button>
        )}
      </div>
    </div>
  );
};

export default InstallPrompt;
